{"conversation_id": "0bfde3c2-a2bd-4da2-ad6e-d3f9e226beac", "user_id": "sam", "title": "Conversation 2025-07-10 15:26:48", "metadata": {}, "messages": [{"role": "user", "content": "exa__exa_search, fc__firecrawl_search, search for AI developments", "timestamp": "2025-07-10T15:26:50.212351"}, {"role": "assistant", "content": "Error: Error code: 400 - {'type': 'error', 'error': {'type': 'invalid_request_error', 'message': 'prompt is too long: 229603 tokens > 200000 maximum'}}", "timestamp": "2025-07-10T15:27:04.540328"}], "created_at": "2025-07-10T15:26:49.000144", "updated_at": "2025-07-10T15:27:04.540328"}