# Multi-MCP HTTP Server Guide

This guide shows how to use the multi-MCP HTTP server that provides both local tools and delegates to third-party MCP servers through a single endpoint.

## Architecture

```
chat_term -> Multi-MCP Server (Port 9000) -> Local Tools + Third-party MCP Servers
```

**Features:**
- Single connection point for multiple MCP servers
- Local tools + third-party tools in one place
- Namespaced third-party tools (e.g., `fc__firecrawl_scrape`)
- Configuration-driven third-party setup
- Parameter mapping and default parameters
- Process spawning and lifecycle management for backend servers
- Robust error handling and automatic reconnection

## Quick Setup

### 1. Start Multi-MCP Server

```bash
# Start with default configuration (local tools only)
python mcp_http_server_multi.py --port 9000

# Start with third-party server configuration
python mcp_http_server_multi.py --port 9000 --config server_config.json

# Enable debug logging
python mcp_http_server_multi.py --port 9000 --config server_config.json --debug
```

### 2. Connect chat_term

```bash
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp
```

## Available Tools

### Local Tools (Always Available)
- `echostring` - Echo a string back
- `echostring_table` - Create a table from items
- `long_task` - Long-running task with progress reporting
- `firecrawl_scrape_text_only` - Local web scraping
- `server_status` - Get server and third-party connection status
- `list_all_tools` - List all available tools (local + third-party)
- `third_party_health` - Check health of third-party connections

### Third-party Tools (When Configured)
- `fc__firecrawl_scrape` - Firecrawl web scraping (if Firecrawl server enabled)
- `fc__firecrawl_search` - Firecrawl web search (if Firecrawl server enabled)
- `fc__firecrawl_map` - Firecrawl site mapping (if Firecrawl server enabled)
- `search__brave_web_search` - Brave web search (if Brave server enabled)
- `search__brave_local_search` - Brave local search (if Brave server enabled)
- `exa__exa_search` - Exa embedding-based search (if Exa server enabled)

## Configuration System

The server uses `server_config.json` with the Augment pattern:

```json
{
    "description": "Enhanced MCP Server Configuration (Augment Pattern)",
    "globalSettings": {
        "defaultTimeout": 120,
        "longRunningToolTimeout": 300,
        "description": "Global timeout settings in seconds"
    },
    "mcpServers": {
        "firecrawl-mcp": {
            "enabled": true,
            "command": "npx",
            "args": ["-y", "firecrawl-mcp"],
            "env": {
                "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
            },
            "namespace": "fc",
            "description": "Firecrawl MCP server spawned via npx",
            "parameterMapping": {
                "firecrawl_search": {
                    "q": "query"
                }
            },
            "defaultParameters": {
                "firecrawl_scrape": {
                    "formats": ["markdown"],
                    "onlyMainContent": true
                }
            }
        }
    }
}
```

### Configuration Options

#### Enable Firecrawl Integration

1. Set environment variable:
```bash
export FIRECRAWL_API_KEY="your_api_key_here"
```

2. Edit `server_config.json` to enable Firecrawl:
```json
{
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": true,
      "namespace": "fc"
    }
  }
}
```

3. Restart the server

#### Add Custom Third-party Server

```json
{
  "mcpServers": {
    "my-custom-server": {
      "enabled": true,
      "command": "python",
      "args": ["my_custom_mcp_server.py", "--port", "8080"],
      "env": {
        "API_KEY": "{MY_API_KEY}"
      },
      "namespace": "custom",
      "description": "My custom MCP server",
      "parameterMapping": {
        "my_tool": {
          "old_param": "new_param"
        }
      },
      "defaultParameters": {
        "my_tool": {
          "timeout": 30
        }
      }
    }
  }
}
```

## Architecture Components

### Multi-MCP Client (`mcp_client_multi.py`)

The client component provides:

1. **Protocol Abstraction**: Separate classes for HTTP, SSE, and stdio protocols
2. **Connection Manager**: Centralized connection lifecycle management
3. **Tool Router**: Intelligent routing of tool calls to appropriate servers
4. **Factory Pattern**: Clean client creation based on protocol type
5. **Error Handling**: Comprehensive error handling and recovery

### Multi-MCP Server (`mcp_http_server_multi.py`)

The server component provides:

1. **Lifespan Management**: Uses FastMCP lifespan for async context management
2. **Structured Initialization**: Organized startup and shutdown procedures
3. **Error Recovery**: Robust error recovery and logging
4. **Async Operations**: Stable async context management
5. **Retry Logic**: Built-in retry for failed connections

## Usage Examples in chat_term

### Local Tools
```
> Use echostring to echo "Hello from multi-MCP server"
> Create a table with echostring_table using items ["A", "B", "C"]
> Run long_task for 10 seconds to test progress reporting
> Get server_status to see all connections
```

### Third-party Tools (if configured)
```
> Use fc__firecrawl_scrape to scrape https://example.com
> Use search__brave_web_search to search for "AI news"
> Use exa__exa_search to find embedding-based results for "machine learning"
> Check third_party_health to verify all connections
> List all available tools with list_all_tools
```

## Server Management

### Starting the Server
```bash
# Basic startup
python mcp_http_server_multi.py --port 9000

# With configuration
python mcp_http_server_multi.py --port 9000 --config server_config.json
```

### Configuration Management
The server uses `server_config.json` for third-party server configuration and parameter management.

## Troubleshooting

### Server Won't Start
1. Check if port 9000 is available
2. Verify configuration file syntax
3. Check environment variables for third-party servers
4. Review server logs for specific errors

### Third-party Tools Not Available
1. Use `server_status` to check third-party connections
2. Use `third_party_health` to test connectivity
3. Verify backend server processes are running
4. Check parameter mappings and default parameters

### Connection Management
The implementation provides stable connection handling through:
- FastMCP lifespan management
- Clean async initialization and cleanup
- Stable connection handling
- Automatic error recovery

## Advanced Features

### Parameter Mapping
Automatically translates parameters between client and server expectations:

```json
"parameterMapping": {
  "firecrawl_search": {
    "q": "query"  // Maps 'q' parameter to 'query'
  }
}
```

### Default Parameters
Automatically applies default values to tool calls:

```json
"defaultParameters": {
  "firecrawl_scrape": {
    "formats": ["markdown"],
    "onlyMainContent": true
  }
}
```

### Global Settings
Configure timeouts and other global settings:

```json
"globalSettings": {
  "defaultTimeout": 120,
  "longRunningToolTimeout": 300
}
```

## Conclusion

The multi-MCP server provides a robust, scalable architecture for integrating multiple MCP servers through a single endpoint. The stable async handling and comprehensive error recovery make it suitable for production use with chat_term and other MCP clients.
