# Multi-MCP Server Setup Guide

This guide provides setup instructions and troubleshooting for the multi-MCP server system.

## System Overview

The multi-MCP implementation consists of two main components:

### Core Components

1. **Multi-MCP Server** (`mcp_http_server_multi.py`) - Main server with FastMCP lifespan management
2. **Multi-MCP Client** (`mcp_client_multi.py`) - Dedicated client with modular architecture

### Architecture Features

1. **Async Context Management**: FastMCP lifespan management for stable operations
2. **Modular Design**: Separate classes for different concerns (ConnectionManager, ToolRouter, etc.)
3. **Protocol Abstraction**: Unified interface for HTTP, SSE, and stdio protocols
4. **Error Recovery**: Comprehensive error handling and automatic reconnection
5. **Connection Management**: Centralized lifecycle management and health monitoring

## Configuration System

The server uses `server_config.json` for configuration management.

### Configuration Format
```json
{
    "description": "Enhanced MCP Server Configuration (Augment Pattern)",
    "globalSettings": {
        "defaultTimeout": 120,
        "longRunningToolTimeout": 300
    },
    "mcpServers": {
        "firecrawl-mcp": {
            "enabled": true,
            "command": "npx",
            "args": ["-y", "firecrawl-mcp"],
            "env": {
                "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
            },
            "namespace": "fc",
            "parameterMapping": {
                "firecrawl_search": {
                    "q": "query"
                }
            },
            "defaultParameters": {
                "firecrawl_scrape": {
                    "formats": ["markdown"],
                    "onlyMainContent": true
                }
            }
        }
    }
}
```

## Setup Steps

### 1. Start Multi-MCP Server

```bash
# Basic startup with local tools only
python mcp_http_server_multi.py --port 9000

# With third-party server configuration
python mcp_http_server_multi.py --port 9000 --config server_config.json

# With debug logging
python mcp_http_server_multi.py --port 9000 --config server_config.json --debug
```

### 2. Connect Client

Connect `chat_term` to the server:

```bash
python -m gaia.gaia_ceto.ceto_v002.chat_term \
  --llm mcp-http \
  --mcp-http-server http://localhost:9000/mcp
```

### 3. Verify Setup

Test that everything works correctly:

```bash
# In chat_term, try these commands:
> Use server_status to check server health
> Use list_all_tools to see available tools
> Use third_party_health to verify backend connections
> Test a third-party tool like fc__firecrawl_scrape
```

## System Behavior

### Stability Features

The server provides stable operation through async lifespan management:
```
INFO: Tool call completed successfully, server remains stable
```

### Error Handling

Automatic reconnection and graceful degradation:
```
WARNING: Connection lost to backend server, attempting reconnection...
INFO: Successfully reconnected to backend server
```

### Logging

Comprehensive logging with detailed context:
```
INFO: Tool fc__firecrawl_scrape completed successfully
DEBUG: Applied parameter mapping: q -> query
DEBUG: Applied default parameters: formats=["markdown"]
```

## Available Tools

### Tool Names
The system provides these tools:
- Local tools: `echostring`, `long_task`, `server_status`
- Third-party tools: `fc__firecrawl_scrape`, `search__brave_web_search`

### Tool Features
- Reliable execution with comprehensive error handling
- Detailed error messages and debugging information
- Stable execution for long-running tasks

## Troubleshooting

### Issue: Server Won't Start

**Symptoms**:
```
ERROR: Failed to start server
```

**Solutions**:
1. Check if port 9000 is available: `lsof -i :9000`
2. Verify configuration file syntax: `python -m json.tool server_config.json`
3. Check environment variables are set: `echo $FIRECRAWL_API_KEY`
4. Enable debug logging: `--debug` flag

### Issue: Third-party Tools Not Available

**Symptoms**:
```
> Use fc__firecrawl_scrape
Tool not found: fc__firecrawl_scrape
```

**Solutions**:
1. Check server status: Use `server_status` tool
2. Verify backend health: Use `third_party_health` tool
3. Check configuration: Ensure `enabled: true` for the server
4. Verify environment variables: Check API keys are set
5. Check logs for connection errors

### Issue: Parameter Mapping Not Working

**Symptoms**:
```
ERROR: Invalid parameter 'q' for tool
```

**Solutions**:
1. Verify parameter mapping in configuration:
   ```json
   "parameterMapping": {
     "firecrawl_search": {
       "q": "query"
     }
   }
   ```
2. Check tool schema with `list_all_tools`
3. Enable debug logging to see parameter transformations

### Issue: Performance Issues

**Symptoms**:
- Slower tool execution
- Timeout errors

**Solutions**:
1. Check global timeout settings in configuration
2. Use `third_party_health` to identify slow connections
3. Monitor server logs for performance issues
4. Consider adjusting timeout values:
   ```json
   "globalSettings": {
     "defaultTimeout": 180,
     "longRunningToolTimeout": 600
   }
   ```

## Support and Debugging

For troubleshooting issues:

### 1. Enable Debug Logging
```bash
python mcp_http_server_multi.py --port 9000 --config server_config.json --debug
```

### 2. Check Server Status
Use the built-in monitoring tools:
- `server_status` - Overall server health
- `third_party_health` - Backend server status
- `list_all_tools` - Available tools inventory

### 3. Review Logs
Monitor server logs for:
- Connection status messages
- Parameter transformation details
- Error messages and stack traces

## System Benefits

### 1. Stability
- Stable server operation with async context management
- Reliable long-running operations
- Consistent tool execution

### 2. Reliability
- Automatic reconnection to backend servers
- Graceful error handling and recovery
- Robust connection lifecycle management

### 3. Maintainability
- Clean, modular architecture
- Comprehensive logging and debugging
- Easy to extend and modify

### 4. Performance
- Efficient connection pooling
- Optimized resource management
- Intelligent tool routing

## Advanced Configuration

### Custom Tool Integration

For custom tool integration:

1. **Local Tools**: Register with `self.mcp.tool()`
2. **Third-party Integration**: Use the `MultiMCPClient` interface
3. **Parameter Processing**: Leverage the parameter mapping system

### Configuration Options

Advanced configuration features:

```json
{
  "globalSettings": {
    "defaultTimeout": 120,
    "longRunningToolTimeout": 300,
    "maxRetries": 3,
    "retryDelay": 5.0
  },
  "mcpServers": {
    "your-server": {
      "enabled": true,
      "description": "Detailed description for monitoring",
      "toolTimeouts": {
        "specific_tool": 600
      }
    }
  }
}
```

### Monitoring Capabilities

The implementation provides comprehensive monitoring:

- Use `server_status` for health checks
- Use `third_party_health` for backend monitoring
- Enable debug logging for detailed operation tracking
- Monitor connection patterns and success rates

## Conclusion

The multi-MCP server implementation provides stability and reliability through clean architecture and comprehensive error handling. The async management and modular design make it a robust foundation for production use with chat_term and other MCP clients.
