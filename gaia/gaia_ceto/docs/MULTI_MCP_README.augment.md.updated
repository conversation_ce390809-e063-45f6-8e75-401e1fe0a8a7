# Multi-MCP Server Architecture

## Overview

The Multi-MCP Server system provides a unified interface for accessing tools from multiple MCP (Model Context Protocol) servers through a single connection point. This architecture enables chat applications to leverage specialized tools from various providers while maintaining a simple, consistent interface.

## Architecture Components

### 1. Multi-MCP Server (`mcp_http_server_multi.py`)
**Role**: Central hub and orchestrator
- **Port**: 9000 (HTTP streaming)
- **Purpose**: Aggregates local tools and delegates to third-party MCP servers
- **Key Features**:
  - Local tool hosting (echostring, long_task, server management)
  - Third-party server delegation with namespacing
  - Config-driven parameter mapping and default values
  - Connection management and health monitoring
  - Automatic reconnection and error handling
  - Async lifespan management with FastMCP

### 2. Multi-MCP Client (`mcp_client_multi.py`)
**Role**: Connection manager and tool router
- **Purpose**: Manages connections to multiple backend MCP servers
- **Protocols Supported**: HTTP, SSE, stdio
- **Key Features**:
  - Protocol abstraction with dedicated client classes
  - Connection pooling and lifecycle management
  - Tool discovery and aggregation
  - Auto-routing and intelligent tool dispatch
  - Graceful error handling and reconnection
  - Factory pattern for client creation

### 3. Chat Terminal (`chat_term.py`)
**Role**: User interface and LLM integration
- **Purpose**: Provides chat interface with MCP tool access
- **Connection**: Single connection to Multi-MCP Server (port 9000)
- **Key Features**:
  - Multiple LLM provider support (Anthropic, OpenAI, Mock)
  - Transparent tool access across all backend servers
  - Conversation management and persistence
  - Progress reporting for long-running tasks

## Execution Flow

### 1. System Initialization

```
1. Multi-MCP Server starts on port 9000
2. Loads server_config.json configuration
3. Validates parameter mappings and default values
4. Creates MultiMCPClient instance for third-party connections
5. Spawns configured third-party MCP servers:
   - Firecrawl MCP (web scraping)
   - Brave Search MCP (web search)
   - Exa MCP (semantic search)
6. Registers local tools and delegated tools with namespacing
7. Starts HTTP streaming server with proper lifespan management
```

### 2. Client Connection Flow

```
1. chat_term connects to http://localhost:9000/mcp
2. Multi-MCP Server provides aggregated tool list:
   - Local tools: echostring, long_task, server_status
   - Delegated tools: search__brave_web_search, fc__firecrawl_scrape
3. chat_term receives unified tool interface
4. LLM can now use any tool transparently
```

### 3. Tool Call Execution Flow

```
User Input: "Search for recent AI developments"
    ↓
chat_term → LLM decides to use search__brave_web_search
    ↓
HTTP POST to Multi-MCP Server (/mcp endpoint)
    ↓
Multi-MCP Server (MCPServerMulti class):
  1. Parses tool name: search__brave_web_search
  2. Identifies server: brave-search-mcp
  3. Maps tool name: brave_web_search
  4. Applies parameter mapping: q → query
  5. Applies default parameters: count = 10
    ↓
MultiMCPClient routes to brave-search-mcp (stdio)
    ↓
Brave Search MCP executes search
    ↓
Results flow back through the chain:
  brave-search-mcp → MultiMCPClient → Multi-MCP Server → chat_term
    ↓
LLM processes results and responds to user
```

## Configuration System

### Server Configuration (`server_config.json`)

The system uses a declarative configuration approach:

```json
{
  "description": "Enhanced MCP Server Configuration (Augment Pattern)",
  "globalSettings": {
    "defaultTimeout": 120,
    "longRunningToolTimeout": 300
  },
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
      },
      "namespace": "fc",
      "description": "Firecrawl MCP server spawned via npx",
      "parameterMapping": {
        "firecrawl_search": {
          "q": "query"
        }
      },
      "defaultParameters": {
        "firecrawl_scrape": {
          "formats": ["markdown"],
          "onlyMainContent": true
        }
      }
    },
    "brave-search-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_SEARCH_API_KEY": "{BRAVE_SEARCH_API_KEY}"
      },
      "namespace": "search",
      "parameterMapping": {
        "brave_web_search": {
          "q": "query"
        }
      },
      "defaultParameters": {
        "brave_web_search": {
          "count": 10
        }
      }
    }
  }
}
```

### Key Configuration Features

1. **Environment Variable Substitution**: `{BRAVE_SEARCH_API_KEY}` → actual API key
2. **Parameter Mapping**: Translates client parameters to server expectations
3. **Default Parameters**: Automatically applies common defaults
4. **Namespacing**: Prevents tool name conflicts across servers
5. **Enable/Disable**: Easy server management without code changes
6. **Global Settings**: Centralized timeout and behavior configuration

## Connection Management

### Protocol Support

1. **HTTP**: Traditional request/response with streaming
2. **SSE (Server-Sent Events)**: Real-time streaming connections
3. **stdio**: Process-based MCP servers (most third-party servers)

### Connection Architecture

```python
class MultiMCPClient:
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.tool_router = ToolRouter(self.connection_manager)

class ConnectionManager:
    def __init__(self):
        self._connections: Dict[str, ServerInfo] = {}
        self._lock = asyncio.Lock()

class ToolRouter:
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
```

### Connection Lifecycle

```
1. Server Spawning (stdio servers):
   - Process creation with environment variables
   - MCP handshake and capability negotiation
   - Tool discovery and registration

2. Connection Monitoring:
   - Health checks and heartbeat monitoring
   - Automatic reconnection on failures
   - Graceful degradation when servers unavailable

3. Cleanup:
   - Proper process termination
   - Resource cleanup and connection closure
   - Error logging and reporting
```

## Tool Namespacing

### Purpose
Prevents conflicts when multiple servers provide similar tools.

### Naming Convention
```
{namespace}__{tool_name}
```

### Examples
- `search__brave_web_search` (Brave Search)
- `fc__firecrawl_scrape` (Firecrawl)
- `exa__exa_search` (Exa)

### Implementation
```python
def _create_namespaced_tool_name(self, namespace: str, tool_name: str) -> str:
    """Create namespaced tool name using double underscore."""
    return f"{namespace}__{tool_name}"
```

## Error Handling and Recovery

### Async Context Management
The implementation provides stable async context management:

```python
class MCPServerMulti:
    def __init__(self, config_file: str = "server_config.json"):
        # Initialize FastMCP with lifespan management
        self.mcp = FastMCP("gaia_aggregate_server", lifespan=self.lifespan)

    @asynccontextmanager
    async def lifespan(self, app):
        """Async lifespan management."""
        try:
            await self.initialize()
            yield
        finally:
            await self.cleanup()
```

### Connection Error Handling
1. **Connection Errors**: Automatic reconnection with exponential backoff
2. **Tool Call Errors**: Detailed error reporting with context
3. **Server Failures**: Graceful degradation, other servers continue working
4. **Parameter Errors**: Clear validation messages and suggestions

## Development and Debugging

### Logging Levels
- **INFO**: Connection status, tool registrations, parameter mappings
- **DEBUG**: Detailed execution flow, parameter transformations
- **ERROR**: Connection failures, tool call errors, configuration issues
- **WARNING**: Reconnection attempts, degraded functionality

### Debug Tools
1. **Server Status Tool**: Real-time health monitoring
2. **Tool Listing**: Complete inventory of available tools
3. **Connection Health**: Individual server status checks
4. **Parameter Tracing**: Detailed parameter transformation logs

## Usage Examples

### Starting the System
```bash
# 1. Start Multi-MCP Server
cd gaia/gaia_ceto/proto_mcp_http
python mcp_http_server_multi.py --port 9000 --config server_config.json

# 2. Connect chat_term
python -m gaia.gaia_ceto.ceto_v002.chat_term \
  --llm mcp-http \
  --mcp-http-server http://localhost:9000/mcp
```

### Tool Usage in Chat
```
> Search for recent developments in artificial intelligence
[Uses search__brave_web_search automatically]

> Scrape the content from https://example.com
[Uses fc__firecrawl_scrape if Firecrawl is enabled]

> Check the status of all connected servers
[Uses server_status local tool]
```

## Benefits of This Architecture

1. **Unified Interface**: Single connection point for multiple specialized tools
2. **Scalability**: Easy addition of new MCP servers without client changes
3. **Flexibility**: Config-driven parameter mapping and server management
4. **Reliability**: Robust error handling and automatic reconnection
5. **Maintainability**: Clean separation of concerns and modular design
6. **Developer Experience**: Rich debugging and monitoring capabilities
7. **Async Stability**: Stable lifespan management and context handling

## Architecture Features

1. **Modular Design**: Separate classes for different concerns
2. **Protocol Abstraction**: Unified interface for HTTP, SSE, and stdio
3. **Factory Pattern**: Clean client creation and management
4. **Lifespan Management**: FastMCP lifespan for stable async operations
5. **Connection Manager**: Centralized connection lifecycle management
6. **Tool Router**: Intelligent routing and load balancing
7. **Error Recovery**: Comprehensive error handling and recovery mechanisms
