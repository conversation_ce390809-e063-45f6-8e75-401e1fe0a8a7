# Multi-MCP Server: Architecture and User Guide

This document provides a comprehensive overview of the multi-MCP (Model Context Protocol) server system, including its architecture, configuration, usage, and technical implementation. This system provides a robust, flexible, and scalable way to provide tools to a language model client like `chat_term`.

## 1. Core Architecture: The Delegator/Hub-and-Spoke Model

The system follows a **Delegator** (or **Hub-and-Spoke**) pattern. Instead of a single, monolithic server, it consists of a central "Hub" that manages and delegates tasks to multiple, specialized "Spoke" servers.

The `chat_term` client is completely unaware of this backend complexity. It connects to a single endpoint, the Multi-MCP Server, and sees a unified list of tools.

### Visualization

```
+-----------+               +------------------------+         +------------------------+
|           |               |                        |         |                        |
| chat_term |<------------->| mcp_http_server_multi  |<------->| Backend Server 1       |
| (Client)  |   (HTTP/SSE)  | (The Hub / Delegator)  |         | (Firecrawl via npx)    |
|           |               |                        |         +------------------------+
+-----------+               +------------------------+
                                      ^
                                      |
                                      |                +------------------------+
                                      +--------------->|                        |
                                                       | Backend Server 2       |
                                                       | (Brave Search via npx) |
                                                       |                        |
                                                       +------------------------+
```

## 2. Component Roles

### a) The Client (`chat_term`)

- **Role**: User Interface / LLM Interaction.
- **Responsibilities**: Connects to the Multi-MCP Server's single endpoint, requests the aggregated tool list, and sends tool call requests.

### b) The Multi-MCP Server (`mcp_http_server_multi.py`)

- **Role**: Central Hub, Aggregator, and Delegator.
- **Responsibilities**:
  - Hosts its own "local" tools (e.g., `echostring`, `long_task`, `server_status`).
  - Manages connections to multiple backend MCP servers via `MultiMCPClient`.
  - Aggregates tool lists from all backend servers.
  - Routes incoming tool calls to the appropriate backend server.
  - Applies parameter mapping and default parameters as configured.
  - Handles errors, retries, and connection management.

### c) The Multi-MCP Client (`mcp_client_multi.py`)

- **Role**: Connection Manager and Protocol Handler within the Hub.
- **Responsibilities**:
  - Manages connections to multiple backend MCP servers simultaneously.
  - Supports multiple protocols: **HTTP**, **SSE** (Server-Sent Events), and **stdio**.
  - Handles process spawning for stdio-based servers (like npx commands).
  - Provides a unified interface for tool calls regardless of backend protocol.

### d) Backend MCP Servers (The "Spokes")

- **Role**: Specialized Tool Providers.
- **Examples**: Firecrawl (web scraping), Brave Search (web search), Exa (semantic search).
- **Characteristics**: Each server is independent, specialized, and can be spawned as a process or accessed via a URL.

## 3. Step-by-Step Execution Flow

1.  **Initialization**:
    - The `mcp_http_server_multi.py` starts and reads `server_config.json`.
    - It creates a `MultiMCPClient` instance.
    - For each enabled backend server, it connects (via URL) or spawns a process (via stdio).
    - It discovers tools from each backend, prefixes them with a namespace (e.g., `fc__firecrawl_scrape`), and registers them.
    - It registers its own local tools.
    - It starts the main HTTP server with async lifespan management.

2.  **Client Connection**:
    - `chat_term` connects to the Hub's `/mcp` endpoint.
    - The Hub returns the fully aggregated and namespaced list of tools.

3.  **Tool Call Execution**:
    - The LLM decides to use a tool, e.g., `search__brave_web_search`.
    - `chat_term` sends the tool call to the Hub.
    - The Hub identifies the `search` namespace and routes the call to the `MultiMCPClient`.
    - The Hub applies any configured **parameter mapping** or **default parameters**.
    - The `MultiMCPClient` forwards the call to the correct backend server (Brave Search MCP).
    - The backend server executes the tool and returns the result.
    - The result flows back through the chain to the `chat_term` client.

## 4. Configuration (`server_config.json`)

The system is configured declaratively using the "Augment Pattern" in `server_config.json`.

```json
{
    "description": "Enhanced MCP Server Configuration (Augment Pattern)",
    "globalSettings": {
        "defaultTimeout": 120,
        "longRunningToolTimeout": 300,
        "maxRetries": 3,
        "retryDelay": 5.0
    },
    "mcpServers": {
        "firecrawl-mcp": {
            "enabled": true,
            "command": "npx",
            "args": ["-y", "firecrawl-mcp"],
            "env": {
                "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
            },
            "namespace": "fc",
            "parameterMapping": {
                "firecrawl_search": {
                    "q": "query"
                }
            },
            "defaultParameters": {
                "firecrawl_scrape": {
                    "formats": ["markdown"],
                    "onlyMainContent": true
                }
            }
        }
    }
}
```

### Key Features:
- **`globalSettings`**: Configure global behaviors like timeouts and connection retries.
- **`mcpServers`**: Define each backend "spoke" server.
- **`enabled`**: Easily enable or disable a backend server.
- **`command` / `args` / `env`**: Define how to spawn stdio-based processes. Environment variables like `{FIRECRAWL_API_KEY}` are resolved from the system environment.
- **`namespace`**: A mandatory prefix to avoid tool name collisions.
- **`parameterMapping`**: Translates parameter names between the client's request and the backend tool's expectation (e.g., maps client `q` to backend `query`).
- **`defaultParameters`**: Automatically applies default values to tool calls if they are not provided by the client.

## 5. Available Tools

### Local Tools (Always Available)
- `echostring`: Echo a string back.
- `echostring_table`: Create a table from a list of items.
- `long_task`: A sample long-running task that reports progress.
- `firecrawl_scrape_text_only`: A simple, local web scraper.
- `server_status`: Get a comprehensive status of the server and all backend connections.
- `list_all_tools`: List all available tools (local and third-party).
- `third_party_health`: Check the health and connectivity of all configured backend servers.

### Third-party Tools (Examples, if configured)
- `fc__firecrawl_scrape`: Advanced web scraping via Firecrawl.
- `search__brave_web_search`: Web search via Brave Search.
- `exa__exa_search`: Semantic, embedding-based search via Exa.

## 6. Setup and Usage

### Step 1: Start the Multi-MCP Server
```bash
# Navigate to the server directory
cd gaia/gaia_ceto/proto_mcp_http

# Start with default configuration (local tools only)
python mcp_http_server_multi.py --port 9000

# Start with a specific configuration for third-party tools
python mcp_http_server_multi.py --port 9000 --config server_config.json

# Enable debug logging for detailed output
python mcp_http_server_multi.py --port 9000 --config server_config.json --debug
```

### Step 2: Connect the `chat_term` Client
```bash
python -m gaia.gaia_ceto.ceto_v002.chat_term \
  --llm mcp-http \
  --mcp-http-server http://localhost:9000/mcp
```

### Step 3: Use the Tools
Once connected, you can use any of the configured tools directly in the chat terminal.
```
> Use server_status to check all connections.
> Use fc__firecrawl_scrape to scrape the content from https://example.com
> Use search__brave_web_search to find recent news about AI.
```

## 7. Troubleshooting

| Issue | Symptom | Solution |
| :--- | :--- | :--- |
| **Server Won't Start** | `ERROR: Failed to start server` | 1. Check if the port is in use (`lsof -i :9000`).<br>2. Validate `server_config.json` syntax.<br>3. Ensure required environment variables are set.<br>4. Run with `--debug` for more logs. |
| **Tool Not Found** | `Tool not found: fc__firecrawl_scrape` | 1. Use the `server_status` tool to check connections.<br>2. Use `third_party_health` to test backend connectivity.<br>3. Ensure the server is `enabled: true` in the config.<br>4. Check server logs for connection errors. |
| **Parameter Error** | `ERROR: Invalid parameter 'q' for tool` | 1. Verify the `parameterMapping` in your config is correct.<br>2. Use `list_all_tools` to inspect the tool's expected schema.<br>3. Enable `--debug` to see parameter transformation logs. |
| **Performance Issues** | Slow tool execution or timeouts. | 1. Check `globalSettings` for timeout values.<br>2. Use `third_party_health` to identify slow backend connections.<br>3. Monitor server logs for performance warnings. |

## 8. Technical Implementation Highlights

- **Async Stability**: The server uses `FastMCP` with proper `lifespan` management (`@asynccontextmanager`) for stable startup, shutdown, and connection handling.
- **Modular Design**: The architecture is highly modular, with distinct classes for `ConnectionManager`, `ToolRouter`, and protocol-specific clients (`HTTPMCPClient`, `StdioMCPClient`).
- **Factory Pattern**: A `ClientFactory` is used to cleanly instantiate the correct protocol client based on the configuration.
- **Error Recovery**: The system features automatic reconnection logic with configurable retries and delays, ensuring graceful degradation if a backend server fails.
- **Rich Monitoring**: Built-in tools like `server_status` and `third_party_health` provide excellent visibility into the system's state for easier debugging.
