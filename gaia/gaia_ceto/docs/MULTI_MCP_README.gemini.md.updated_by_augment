# Multi-MCP Execution Model

This document describes the architecture and execution model of the multi-MCP (Model Context Protocol) system. This system is designed to be a robust, flexible, and scalable way to provide tools to a language model client like `chat_term`.

## 1. Core Architecture: The Delegator/Hub-and-Spoke Model

The system follows a **Delegator** (or **Hub-and-Spoke**) pattern with three primary components:

1. **The Client (`chat_term`)**: The user-facing application that communicates with a language model.
2. **The Multi-MCP Server (The "Hub")**: A central, delegating server that manages and aggregates tools from other servers. This is implemented in `mcp_http_server_multi.py`.
3. **Backend MCP Servers (The "Spokes")**: Multiple, independent, and specialized servers that each provide a specific set of tools (e.g., for web search, file system access, etc.).

The key principle is **decoupling**. The `chat_term` client is completely unaware of the complexity of the backend. It only ever connects to the single Multi-MCP Server hub.

### Visualization

```
+-----------+               +------------------------+         +------------------------+
|           |               |                        |         |                        |
| chat_term |<------------->| mcp_http_server_multi  |<------->| Backend Server 1       |
| (Client)  |   (HTTP/SSE)  | (The Hub / Delegator)  |         | (Firecrawl via npx)    |
|           |               |                        |         +------------------------+
+-----------+               +------------------------+
                                      ^
                                      |
                                      |                +------------------------+
                                      +--------------->|                        |
                                                       | Backend Server 2       |
                                                       | (Brave Search via npx) |
                                                       |                        |
                                                       +------------------------+
```

## 2. Component Roles

### a) The Client (`chat_term`)

- **Role**: User Interface / LLM Interaction.
- **Responsibilities**:
  - Connect to a *single* MCP endpoint: the URL of the Multi-MCP Server.
  - Request the list of available tools from that single endpoint.
  - Send tool call requests to that endpoint.
  - Receive and display the results streamed from that endpoint.
- **Key Characteristic**: It has no knowledge of the backend servers. It sees a single, unified list of tools.

### b) The Multi-MCP Server (`mcp_http_server_multi.py`)

- **Role**: Central Hub, Aggregator, and Delegator.
- **Responsibilities**:
  - Host its own "local" tools (e.g., `echostring`, `long_task`, `server_status`).
  - Manage connections to multiple backend MCP servers via `MultiMCPClient`.
  - Aggregate tool lists from all backend servers and present them as a unified interface.
  - Route incoming tool calls to the appropriate backend server.
  - Apply parameter mapping and default parameters as configured.
  - Handle errors, retries, and connection management.
  - Provide server status and health monitoring tools.

### c) The Multi-MCP Client (`mcp_client_multi.py`)

- **Role**: Connection Manager and Protocol Handler.
- **Responsibilities**:
  - Manage connections to multiple backend MCP servers simultaneously.
  - Support multiple protocols: HTTP, SSE (Server-Sent Events), and stdio.
  - Handle process spawning for stdio-based servers (like npx commands).
  - Provide a unified interface for tool calls regardless of backend protocol.
  - Manage connection lifecycle, health monitoring, and automatic reconnection.
  - Route tool calls to the correct backend server.

### d) Backend MCP Servers (The "Spokes")

- **Role**: Specialized Tool Providers.
- **Examples**:
  - **Firecrawl MCP**: Web scraping and crawling tools.
  - **Brave Search MCP**: Web search capabilities.
  - **Exa MCP**: Semantic search and embedding-based search.
  - **Filesystem MCP**: File system operations.
- **Characteristics**:
  - Each server is independent and specialized.
  - They can be spawned as processes (stdio) or accessed via URL (HTTP/SSE).
  - They provide their own set of tools with specific schemas.

## 3. Execution Flow

### Step 1: System Initialization

```
1. Multi-MCP Server (`mcp_http_server_multi.py`) starts
2. Loads configuration from `server_config.json`
3. Creates MultiMCPClient instance
4. For each enabled backend server in config:
   a. Spawns the server process (if stdio) or connects to URL (if HTTP/SSE)
   b. Performs MCP handshake and capability negotiation
   c. Discovers available tools from the backend server
   d. Registers tools with namespace prefix (e.g., `fc__firecrawl_scrape`)
5. Registers local tools (e.g., `echostring`, `server_status`)
6. Starts HTTP server on port 9000 with proper async lifespan management
```

### Step 2: Client Connection

```
1. chat_term connects to http://localhost:9000/mcp
2. Multi-MCP Server responds with aggregated tool list:
   - Local tools: echostring, long_task, server_status, etc.
   - Backend tools: fc__firecrawl_scrape, search__brave_web_search, etc.
3. chat_term now has access to all tools through a single interface
```

### Step 3: Tool Call Execution

```
User: "Search for recent AI developments"
    ↓
LLM decides to use: search__brave_web_search
    ↓
chat_term sends HTTP POST to Multi-MCP Server
    ↓
Multi-MCP Server:
  1. Parses tool name: search__brave_web_search
  2. Identifies namespace: "search" → brave-search-mcp server
  3. Strips namespace: brave_web_search
  4. Applies parameter mapping: q → query (if configured)
  5. Applies default parameters: count = 10 (if configured)
  6. Routes call to MultiMCPClient
    ↓
MultiMCPClient:
  1. Identifies target server: brave-search-mcp
  2. Uses appropriate protocol client (stdio in this case)
  3. Sends tool call to backend server
    ↓
Brave Search MCP Server:
  1. Executes search with Brave Search API
  2. Returns results
    ↓
Results flow back:
  Brave Search → MultiMCPClient → Multi-MCP Server → chat_term
    ↓
LLM processes results and responds to user
```

## 4. Key Features

### a) Namespacing

Tools from backend servers are prefixed with their namespace to avoid conflicts:
- `fc__firecrawl_scrape` (Firecrawl namespace)
- `search__brave_web_search` (Search namespace)
- `exa__exa_search` (Exa namespace)

### b) Parameter Mapping

The system can automatically translate parameter names between what the client expects and what the backend server requires:

```json
"parameterMapping": {
  "firecrawl_search": {
    "q": "query"  // Client sends 'q', backend expects 'query'
  }
}
```

### c) Default Parameters

Automatically apply default values to tool calls:

```json
"defaultParameters": {
  "firecrawl_scrape": {
    "formats": ["markdown"],
    "onlyMainContent": true
  }
}
```

### d) Protocol Flexibility

Supports multiple connection types:
- **stdio**: Process-spawned servers (most common for npx-based tools)
- **HTTP**: Traditional HTTP-based MCP servers
- **SSE**: Server-Sent Events for real-time streaming

### e) Error Handling

- Automatic reconnection with exponential backoff
- Graceful degradation when backend servers fail
- Comprehensive logging and debugging tools
- Async context management with FastMCP lifespan

## 5. Configuration

The system is configured via `server_config.json`:

```json
{
  "description": "Enhanced MCP Server Configuration (Augment Pattern)",
  "globalSettings": {
    "defaultTimeout": 120,
    "longRunningToolTimeout": 300
  },
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
      },
      "namespace": "fc",
      "description": "Firecrawl MCP server spawned via npx"
    }
  }
}
```

## 6. Benefits of This Architecture

1. **Simplicity for Clients**: `chat_term` only needs to connect to one endpoint.
2. **Modularity**: Backend servers can be added, removed, or updated independently.
3. **Scalability**: Easy to add new specialized tools without changing client code.
4. **Flexibility**: Support for different protocols and connection types.
5. **Robustness**: Comprehensive error handling and recovery mechanisms.
6. **Configuration-Driven**: Easy to manage and modify server configurations.
7. **Namespace Safety**: No tool name conflicts between different servers.

## 7. Implementation Features

### a) Modular Architecture
- Separate classes for different concerns (ConnectionManager, ToolRouter, etc.)
- Protocol abstraction with dedicated client classes
- Factory pattern for client creation

### b) Async Management
- FastMCP lifespan management for stable async operations
- Structured initialization and cleanup procedures
- Stable connection handling

### c) Error Recovery
- Automatic reconnection with intelligent retry logic
- Graceful degradation when servers are unavailable
- Comprehensive error logging and reporting

### d) Connection Management
- Centralized connection lifecycle management
- Health monitoring and heartbeat checks
- Support for multiple concurrent connections

This architecture provides a robust, scalable foundation for integrating multiple MCP servers while maintaining simplicity for client applications.
