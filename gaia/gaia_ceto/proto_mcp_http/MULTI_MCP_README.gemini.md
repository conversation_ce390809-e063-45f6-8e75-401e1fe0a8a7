# Multi-MCP Server and Client Architecture

This document describes the execution model and interoperability of the Multi-MCP (Model-Call-Protocol) system. The architecture is designed around an aggregate server that acts as a single gateway to multiple backend MCP servers, providing a unified toolset to a connecting client.

## 1. High-Level Overview

The system follows a sophisticated client-server model, which can be described as a "server of servers" or an "aggregate server" pattern.

-   **`mcp_http_server_multi.py` (The Aggregate Server)**: This is the central component. It starts a primary MCP server that not only serves its own "local" tools but also connects to other, independent MCP servers (we'll call these "third-party servers"). It dynamically registers the tools from these third-party servers, making them available to its own clients under a unified, namespaced API.

-   **`mcp_client_multi.py` (The Multi-Client Library)**: This is a powerful, protocol-agnostic client library. The Aggregate Server *uses this library internally* to connect to the various third-party servers. A standalone client application could also use this library to connect to one or more MCP servers directly.

-   **`server_config.json` (The Configuration)**: This file is the brain of the operation. It tells the Aggregate Server which third-party servers to connect to, how to connect to them (URL, command-line process), and how to handle their tools (e.g., what namespace to use).

-   **Client Applications (e.g., `mcp_http_client_light.py`, `chat_term`)**: These are the end-users of the system. They connect *only* to the single Aggregate Server, unaware of the complex delegation happening in the background.

```mermaid
graph TD
    subgraph "End-User Client"
        ClientApp["Client App (e.g., chat_term)"]
    end

    subgraph "Aggregate MCP Server (mcp_http_server_multi.py)"
        MainServer["FastMCP Instance"]
        LocalTools["Local Tools (e.g., echo, server_info)"]
        DelegatedTools["Delegated Tools (e.g., fc__firecrawl_scrape)"]
        MultiClient["Internal MultiMCPClient (mcp_client_multi.py)"]

        MainServer --- LocalTools
        MainServer --- DelegatedTools
        DelegatedTools -- delegates to --> MultiClient
    end

    subgraph "Third-Party MCP Servers (Defined in server_config.json)"
        Server1["Firecrawl MCP (Process)"]
        Server2["Exa MCP (Process)"]
        Server3["Hosted Service (URL)"]
    end

    ClientApp -- "Single HTTP Connection" --> MainServer
    MultiClient -- "Manages Multiple Connections (HTTP, SSE, Stdio)" --> Server1
    MultiClient --> Server2
    MultiClient --> Server3
```

## 2. Core Components and Their Roles

### `mcp_http_server_multi.py` - The Aggregate Server
-   **Primary Role**: To act as a single, unified entry point for clients.
-   **Initialization**:
    1.  Starts its own `FastMCP` instance.
    2.  Registers its own built-in tools like `echo` and `server_info`.
    3.  Reads `server_config.json` to discover the third-party servers.
    4.  Initializes an internal instance of `MultiMCPClient`.
-   **Connection to Backends**:
    1.  For each enabled server in the config, it uses its internal `MultiMCPClient` to establish a connection. This can be via HTTP/SSE to a URL or by spawning a local process and communicating via `stdio`.
-   **Tool Delegation**:
    1.  Once connected to a third-party server, it retrieves the list of available tools.
    2.  For each third-party tool, it dynamically creates a new "delegated" tool function on its own server.
    3.  This new tool is given a **namespaced name** (e.g., `firecrawl_scrape` becomes `fc__firecrawl_scrape`) to avoid conflicts.
    4.  When a client calls this namespaced tool, the delegated function executes, calling the *original* tool on the correct third-party server via the `MultiMCPClient`.

### `mcp_client_multi.py` - The Multi-Connection Client
-   **Primary Role**: To manage simultaneous connections to multiple MCP servers using various protocols (HTTP, SSE, Stdio).
-   **Key Features**:
    -   **`ConnectionManager`**: Handles the lifecycle of each server connection.
    -   **`ClientFactory`**: Creates the appropriate protocol-specific client wrapper (`HTTPMCPClient`, `SSEMCPClient`, `StdioMCPClient`).
    -   **`ToolRouter`**: Keeps track of which tools belong to which server, enabling auto-routing.
-   **Usage**: It is used internally by the Aggregate Server but is robust enough to be the basis for any client application that needs to talk to more than one MCP server.

### `mcp_http_clientlib.py` - The Basic HTTP Client Library
-   **Primary Role**: Provides the fundamental logic for a single client-to-server HTTP connection.
-   **Usage**: It is used by the `HTTPMCPClient` wrapper inside `mcp_client_multi.py`. It handles the low-level details of the MCP handshake, tool calls, and response parsing over a streamable HTTP connection.

### `server_config.json` - The Configuration File
-   **Primary Role**: To define the entire backend architecture declaratively.
-   **Key Sections**:
    -   **`globalSettings`**: Defines system-wide defaults for timeouts.
    -   **`mcpServers`**: An object where each key is a unique ID for a third-party server. The value contains all information needed to connect and integrate it:
        -   `enabled`: A boolean to easily toggle servers on or off.
        -   `command`/`args`/`env`: For servers that are spawned as local processes.
        -   `url`/`protocol`: For servers that are accessed via a network URL.
        -   `namespace`: The prefix to use for all tools from this server.
        -   `parameterMapping`/`defaultParameters`: Allows for transforming tool arguments on the fly, adapting third-party tools to better fit the aggregate server's conventions.

## 3. Execution Model and Interoperability

The system's execution flow is as follows:

1.  **Server Startup**:
    -   A user runs `python mcp_http_server_multi.py`.
    -   The `MCPServerMulti` class is instantiated. It immediately loads `server_config.json`.
    -   It starts a `uvicorn` server, which hosts the `FastMCP` application.

2.  **Backend Connection Phase (Lifespan Startup)**:
    -   As the `uvicorn` server starts up, the `lifespan` manager in `MCPServerMulti` is triggered.
    -   It instantiates its internal `MultiMCPClient`.
    -   It iterates through the enabled servers in its configuration and asynchronously attempts to connect to each one using the `MultiMCPClient`.
    -   For each successful connection, it fetches the list of tools and dynamically creates and registers the namespaced, delegated tool functions.

3.  **Client Connection**:
    -   A client application (like `chat_term`) establishes a single, standard MCP HTTP connection to the `mcp_http_server_multi.py` endpoint (e.g., `http://localhost:9000/mcp`).
    -   The client performs a `list_tools` call. The Aggregate Server returns a single, unified list containing its own local tools *and* all the namespaced tools from the successfully connected third-party servers.

4.  **Tool Call and Delegation**:
    -   The client decides to call a tool, for example, `fc__firecrawl_scrape`.
    -   It sends a standard `call_tool` request to the Aggregate Server.
    -   The Aggregate Server's `FastMCP` instance invokes the corresponding delegated function.
    -   This function:
        a.  Identifies the original server (`firecrawl-mcp`) and original tool name (`firecrawl_scrape`).
        b.  Applies any parameter mappings or default values defined in the configuration.
        c.  Uses the internal `MultiMCPClient` to send the `call_tool` request to the *actual* Firecrawl MCP server.
        d.  Awaits the response from the Firecrawl server.
    -   The response is received by the `MultiMCPClient`, passed back to the delegated function, and finally sent back to the original client as the result of its tool call.

This architecture effectively **decouples** the end-client from the complexity of the backend tool ecosystem. The Aggregate Server acts as a robust and configurable middleware, providing a stable and unified API.
